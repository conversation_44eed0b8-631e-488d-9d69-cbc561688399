import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:praja/features/video_posters/models/video_poster_element.dart';
import 'package:flick_video_player/flick_video_player.dart';
import 'package:video_player/video_player.dart';
import 'package:praja/utils/logger.dart';
import 'package:praja/presentation/view_detector.dart';

class VideoPosterElementWidget extends StatefulWidget {
  final VideoPosterElement element;
  final VoidCallback? onUserWatchedVideoTillTriggerDuration;
  final String uniqueId;
  final int triggerDurationSeconds;

  const VideoPosterElementWidget({
    super.key,
    required this.element,
    this.onUserWatchedVideoTillTriggerDuration,
    required this.uniqueId,
    this.triggerDurationSeconds = 3,
  });

  @override
  State<VideoPosterElementWidget> createState() =>
      _VideoPosterElementWidgetState();
}

class _VideoPosterElementWidgetState extends State<VideoPosterElementWidget> {
  FlickManager? _flickManager;
  bool _isVideoMuted = true; // Local state to track mute status
  bool _isVideoPaused = false; // Local state to track play/pause status
  bool _hasTriggeredDurationCallback =
      false; // Track if trigger duration callback has been triggered
  Duration _lastPosition = Duration.zero; // Track last known position

  @override
  void initState() {
    super.initState();
    // Initialize video as unmuted by default for video posters
    _isVideoMuted = false;

    if (widget.element.type == VideoPosterElementType.video) {
      _initializeVideoPlayer();
    }
  }

  void _onView(int viewCount) {
    if (_flickManager != null) {
      _flickManager?.flickControlManager?.play();
      printDebug('Video element resumed: ${widget.element.url}');
    }
  }

  void _onOutOfView() {
    if (_flickManager != null) {
      _flickManager?.flickControlManager?.pause();
      printDebug('Video element paused: ${widget.element.url}');
    }
  }

  void _initializeVideoPlayer() {
    _flickManager = FlickManager(
      videoPlayerController: VideoPlayerController.networkUrl(
        Uri.parse(widget.element.url),
        videoPlayerOptions: VideoPlayerOptions(mixWithOthers: true),
      )..setLooping(true),
      autoPlay: true,
      autoInitialize: true,
    );

    // Add listener to track play/pause state changes and video position
    _flickManager?.flickVideoManager?.addListener(() {
      if (mounted) {
        final isPlaying = _flickManager?.flickVideoManager?.isPlaying ?? false;
        if (_isVideoPaused == isPlaying) {
          setState(() {
            _isVideoPaused = !isPlaying;
          });
        }

        // Track video position for 3-second callback
        _trackVideoPosition();
      }
    });

    // Set audio state to unmuted by default for video posters
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        if (_isVideoMuted) {
          _flickManager?.flickControlManager?.mute();
        } else {
          _flickManager?.flickControlManager?.unmute();
        }
      }
    });
  }

  void _trackVideoPosition() {
    final videoManager = _flickManager?.flickVideoManager;
    if (videoManager == null || !videoManager.isVideoInitialized) return;

    final currentPosition =
        videoManager.videoPlayerValue?.position ?? Duration.zero;
    final isPlaying = videoManager.isPlaying;

    // Only track if video is playing and position has changed
    if (isPlaying && currentPosition != _lastPosition) {
      _lastPosition = currentPosition;

      // Check if trigger duration has been watched
      if (!_hasTriggeredDurationCallback &&
          currentPosition.inSeconds >= widget.triggerDurationSeconds) {
        _hasTriggeredDurationCallback = true;
        widget.onUserWatchedVideoTillTriggerDuration?.call();
      }
    }
  }

  @override
  void dispose() {
    _flickManager?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final left = widget.element.x;
    final top = widget.element.y;
    final width = widget.element.width;
    final height = widget.element.height;

    Widget content;

    if (widget.element.type == VideoPosterElementType.video &&
        _flickManager != null) {
      content = ViewDetector(
        uniqueId: widget.uniqueId,
        onView: _onView,
        onOutOfView: _onOutOfView,
        builder: (context, isVisible) {
          return Stack(
            children: [
              // Video player
              GestureDetector(
                onTap: () {
                  // Toggle play/pause on tap
                  if (_flickManager?.flickVideoManager?.isPlaying == true) {
                    _flickManager?.flickControlManager?.pause();
                  } else {
                    _flickManager?.flickControlManager?.play();
                  }
                },
                child: FlickVideoPlayer(
                  flickManager: _flickManager!,
                  flickVideoWithControls: const FlickVideoWithControls(
                    controls:
                        SizedBox(), // No controls for video poster elements
                  ),
                ),
              ),

              // Pause icon in center when video is paused
              if (_isVideoPaused)
                Center(
                  child: GestureDetector(
                    onTap: () {
                      // Resume video when pause icon is tapped
                      _flickManager?.flickControlManager?.play();
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.5),
                        shape: BoxShape.circle,
                      ),
                      child: const Padding(
                        padding: EdgeInsets.all(8.0),
                        child: Icon(
                          Icons.play_arrow,
                          size: 24,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ),

              // Mute/Unmute toggle button at bottom-right
              Positioned(
                bottom: 4,
                right: 4,
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      // Toggle local mute state
                      _isVideoMuted = !_isVideoMuted;

                      // Apply the new audio state to this video
                      if (_isVideoMuted) {
                        _flickManager?.flickControlManager?.mute();
                      } else {
                        _flickManager?.flickControlManager?.unmute();
                      }
                    });
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.5),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(4.0),
                      child: Icon(
                        _isVideoMuted
                            ? Icons.volume_off_rounded
                            : Icons.volume_up_rounded,
                        size: 14,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      );
    } else {
      // Photo element
      content = CachedNetworkImage(
        imageUrl: widget.element.url,
        fit: BoxFit.cover,
        placeholder: (context, url) => Container(
          color: Colors.grey[300],
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
        errorWidget: (context, url, error) => Container(
          color: Colors.grey[300],
          child: const Icon(Icons.error),
        ),
        imageBuilder: (context, imageProvider) => Container(
          decoration: BoxDecoration(
            image: DecorationImage(
              image: imageProvider,
              fit: BoxFit.cover,
            ),
          ),
        ),
      );
    }

    return Positioned(
      left: left,
      top: top,
      width: width,
      height: height,
      child: _buildBorder(child: content),
    );
  }

  Widget _buildBorder({required Widget child}) {
    final border = widget.element.border;
    if (border == null) {
      return child;
    }

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(border.radius),
        border: Border.all(
          color: Color(border.color),
          width: border.width,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(border.radius),
        child: child,
      ),
    );
  }
}
