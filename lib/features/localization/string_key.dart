import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'lang_code.dart';
import 'string_store.dart';

// Naming Convention
// <feature>_<ui_element_name>

enum StringKey {
  // Home Page String Keys
  navTrendsLabel('nav_trends_label'),
  navPostersLabel('nav_posters_label'),
  navCirclesLabel('nav_circles_label'),
  navNotificationsLabel('nav_notifications_label'),
  navChatsLabel('nav_chats_label'),
  homeCreatePostFabLabel('home_create_post_fab_label'),
  trendTutorialDefaultText('trend_tutorial_default_text'),

  // Posters Page String Keys
  posterShareButtonLabel('poster_share_button_label'),
  posterDownloadButtonLabel('poster_download_button_label'),
  posterWhatsappShareButtonLabel('poster_whatsapp_share_button_label'),
  noPostersFound('no_posters_found'),
  postersGetPremiumText('posters_get_premium_text'),
  postersFeedHelpTutorialLabel('posters_feed_help_tutorial_label'),
  postersFeedHelpTutorialText('posters_feed_help_tutorial_text'),
  posterTitle('poster_title'),

  // Navigation Drawer String Keys
  localeSwitcherHeading('locale_switcher_heading'),

  navDrawerOptionProfileLabel('nav_drawer_option_profile_label'),
  navDrawerOptionPremiumLabel('nav_drawer_option_premium_label'),
  navDrawerOptionReferLabel('nav_drawer_option_refer_label'),
  navDrawerOptionRatingLabel('nav_drawer_option_rating_label'),
  navDrawerOptionCustomerCareLabel('nav_drawer_option_customer_care_label'),
  navDrawerLogoutConfirmationDialogHeading(
      'nav_drawer_logout_confirmation_dialog_heading'),
  navDrawerLogoutConfirmationPositiveButtonLabel(
      'nav_drawer_logout_confirmation_positive_button_label'),
  navDrawerLogoutConfirmationNegativeButtonLabel(
      'nav_drawer_logout_confirmation_negative_button_label'),
  navDrawerVersionCopiedToast('nav_drawer_version_copied_toast'),
  navDrawerPrivacyPolicyLabel('nav_drawer_privacy_policy_label'),
  navDrawerTermsOfUseLabel('nav_drawer_terms_of_use_label'),
  navDrawerSettingsLabel('nav_drawer_settings_label'),
  navDrawerSupportLabel('nav_drawer_support_label'),
  navDrawerLogoutLabel('nav_drawer_logout_label'),
  navDrawerDeleteAccountLabel('nav_drawer_delete_account_label'),
  accountDeleteConfirmationDialogHeading(
      'account_delete_confirmation_dialog_heading'),
  accountDeleteConfirmationPositiveButtonLabel(
      'account_delete_confirmation_positive_button_label'),
  accountDeleteConfirmationNegativeButtonLabel(
      'account_delete_confirmation_negative_button_label'),

  // Feed Strings
  trendingFeedShimmerText('trending_feed_shimmer_text'),
  feedErrorMessage('feed_error_message'),
  postersFeedErrorMessage('posters_feed_error_message'),
  noOpinionsFoundText('no_opinions_found_text'),

  // Post Strings
  noPostsFoundText('no_posts_found_text'),
  postNotFoundText('post_not_found_text'),
  postDetailsLabel('post_details_label'),
  commentSettingsLabel('comment_settings_label'),
  reportPostLabel('report_post_label'),
  blockUserLabel('block_user_label'),
  reportUserLabel('report_user_label'),
  doYouWantToDeletePostText('do_you_want_to_delete_post_text'),
  thisPostDeletedText('this_post_deleted_text'),
  postDeletedText('post_deleted_text'),
  tagRemovedText('tag_removed_text'),

  // Comment Related Strings
  commentDeleteConfirmationText('comment_delete_confirmation_text'),
  commentDeletedToast('comment_deleted_toast'),

  // Circle Related Strings
  circleMembersLabel('circle_members_label'),
  noMembersFoundText('no_members_found_text'),
  selectFeedLabel('select_feed_label'),
  areYouWantToLeaveCircleText('are_you_want_to_leave_circle_text'),
  channelLabel('channel_label'),
  yourCirclesLabel('your_circles_label'),
  circleRecommendationsText('circle_recommendations_text'),
  myCirclesLabel('my_circles_label'),
  moreCirclesLabel('more_circles_label'),
  suggestedCirclesLabel('suggested_circles_label'),
  myCirclesTutorialText('my_circles_tutorial_text'),
  suggestedCirclesTutorialText('suggested_circles_tutorial_text'),

  // Post Create Page Strings
  rulesPopupTimeSecondsLabel('rules_popup_time_seconds_label'),
  alreadyInGroupWarnText('already_in_group_warn_text'),
  selectedCirclesText('selected_circles_text'),
  createPostHintText('create_post_hint_text'),
  photoUploadingText('photo_uploading_text'),
  photosUploadingText('photos_uploading_text'),
  videoUploadingText('video_uploading_text'),
  waitingLabel('waiting_label'),
  postGettingReadyText('post_getting_ready_text'),
  uploadFailedText('upload_failed_text'),
  wantToLeaveText('want_to_leave_text'),
  youWillLoseChangesText('you_will_lose_changes_text'),

  // Messaging Strings
  messageDeletedText('message_deleted_text'),
  selfMessageReplyText('self_message_reply_text'),
  seePhotosLabel('see_photos_label'),
  yesterdayLabel('yesterday_label'),
  unreadLabel('unread_label'),
  latestUpdatesLabel('latest_updates_label'),
  leaveChannelConfirmationText('leave_channel_confirmation_text'),
  blockedUserCannotMessageYouText('blocked_user_cannot_message_you_text'),
  leaveChannelText('leave_channel_text'),
  chatSettingsLabel('chat_settings_label'),
  channelSettingsLabel('channel_settings_label'),
  viewRemainingMembersLabel('view_remaining_members_label'),
  unableToChatWithBlockedUserText('unable_to_chat_with_blocked_user_text'),
  unableToChatWithBlockedByUserText('unable_to_chat_with_blocked_by_user_text'),
  uploadPosterLabel('upload_poster_label'),
  blockUserDynamicText('block_user_dynamic_text'),
  blockUserDynamicQuestionText('block_user_dynamic_question_text'),
  initialMessageBlockInPrefixText('initial_message_block_in_prefix_text'),
  initialMessageBlockInSuffixText('initial_message_block_in_suffix_text'),
  chatStartedOnPrefixText('chat_started_on_prefix_text'),
  chatStartedOnSuffixText('chat_started_on_suffix_text'),
  settingsHintText('settings_hint_text'),
  doYouWantToReportText('do_you_want_to_report_text'),
  reportLastMessagesToPrajaText('report_last_messages_to_praja_text'),
  shareInPrajaChatText('share_in_praja_chat_text'),
  myChatsLabel('my_chats_label'),
  otherChatsLabel('other_chats_label'),
  sendNewMessageText('send_new_message_text'),
  tapHereToStartNewChatText('tap_here_to_start_new_chat_text'),
  noChatsFoundText('no_chats_found_text'),

  // Premium Journey Strings
  premiumLabel('premium_label'),
  prajaPremiumLabel('praja_premium_label'),
  noOneViewedYourProfileText('no_one_viewed_your_profile_text'),
  selectAtLeastOneCancelReasonText('select_at_least_one_cancel_reason_text'),
  yourPremiumMembershipCancelledText('your_premium_membership_cancelled_text'),
  paymentsUpiAppLabel('payments_upi_app_label'),
  premiumUnableToOpenPhoneErrorMessage(
      'premium_unable_to_open_phone_error_message'),
  paymentsUpiAppPickerTitle('payments_upi_app_picker_title'),

  // Payment Flow Strings
  yourPaymentFailedText('your_payment_failed_text'),

  // Party Suggestion Screen
  partySuggestionsScreenHeaderText('party_suggestions_screen_header_text'),
  areYouPoliticalFanText('are_you_political_fan_text'),
  joinForLatestPoliticalUpdatesText('join_for_latest_political_updates_text'),
  knowWhatIsHappeningText('know_what_is_happening_text'),

  // User Profile Strings
  profileLabel('profile_label'),
  saveProfileChangesButtonLabel('save_profile_changes_button_label'),
  blockedByUserText('blocked_by_user_text'),
  blockedUserText('blocked_user_text'),
  unblockUserText('unblock_user_text'),
  messageLabel('message_label'),
  shareProfileLabel('share_profile_label'),
  yourProfileLabel('your_profile_label'),
  notFollowingAnyoneText('not_following_anyone_text'),
  profilePictureRemoveConfirmationText(
      'profile_picture_remove_confirmation_text'),
  profileSaveErrorToast('profile_save_error_toast'),
  pictureNotSelectedToast('picture_not_selected_toast'),
  youAreNotABadgeUserText('you_are_not_a_badge_user_text'),

  // App Update Strings
  appUpdateTitle('app_update_title'),
  appUpdateMessage('app_update_message'),
  appUpdateButtonLabel('app_update_button_label'),

  // Permission Strings
  microphoneRequestPermissionText('microphone_request_permission_text'),

  speechDialogMessage('speech_dialog_message'),
  speechCompletedLabel('speech_completed_label'),

  // Onboarding Strings
  loginLabel('login_label'),
  nameNotEmptyWarnText('name_not_empty_warn_text'),
  otpNotEmptyWarnText('otp_not_empty_warn_text'),
  otpNotValidWarnText('otp_not_valid_warn_text'),
  otpResendText('otp_resend_text'),
  selectDistrictText('select_district_text'),
  selectMandalText('select_mandal_text'),
  selectVillageText('select_village_text'),
  selectDistrictTitle('select_district_title'),
  selectMandalTitle('select_mandal_title'),
  selectVillageTitle('select_village_title'),
  signUpInfoText('sign_up_info_text'),
  signUpInfoSuffixText('sign_up_info_suffix_text'),
  loginInfoPrefixText('login_info_prefix_text'),
  loginInfoSuffixText('login_info_suffix_text'),

  // Generic Strings
  cameraLabel('camera_label'),
  seeMoreLabel('see_more_label'),
  retryLabel('retry_label'),
  goBackLabel('go_back_label'),
  verifiedLabel('verified_label'),
  notVerifiedLabel('not_verified_label'),
  followUserLabel('follow_user_label'),
  unfollowUserLabel('unfollow_user_label'),
  followingUserLabel('following_user_label'),
  trendedUsersText('trended_users_text'),
  followMeOnPrajaSuffixText('follow_me_on_praja_suffix_text'),
  attachYourProfilePhotoLabel('attach_your_profile_photo_label'),
  noContactsFoundText('no_contacts_found_text'),
  joinLabel('join_label'),
  reportLabel('report_label'),
  continueButtonLabel('continue_button_label'),
  skipButtonLabel('skip_button_label'),
  yesLabel('yes_label'),
  noLabel('no_label'),
  noAlternateLabel('no_alternate_label'),
  leaveLabel('leave_label'),
  maleLabel('male_label'),
  femaleLabel('female_label'),
  otherLabel('other_label'),
  nameLabel('name_label'),
  phoneNumberLabel('phone_number_label'),
  shortBioLabel('short_bio_label'),
  birthdayLabel('birthday_label'),
  genderLabel('gender_label'),
  goToSettingsLabel('go_to_settings_label'),
  shareLabel('share_label'),
  joinedLabel('joined_label'),
  inviteLabel('invite_label'),
  nextLabel('next_label'),
  finishLabel('finish_label'),
  okayLabel('okay_label'),
  deleteLabel('delete_label'),
  captureLoadingText('capture_loading_text'),
  helpLabel('help_label'),
  whatsappLabel('whatsapp_label'),
  conditionsLabel('conditions_label'),
  privacyLabel('privacy_label'),
  cancelLabel('cancel_label'),
  unblockLabel('unblock_label'),
  blockLabel('block_label'),
  allowLabel('allow_label'),
  doSearchText('do_search_text'),
  othersLabel('others_label'),
  otherAppsLabel('other_apps_label'),
  publicLabel('public_label'),
  galleryLabel('gallery_label'),
  supportLabel('support_label'),
  selectReasonToast('select_reason_toast'),
  reportFinishedToastText('report_finished_toast_text'),

  //Generic Plural Strings
  userFollowersCountSuffixText('user_followers_count_suffix_text'),
  postLabel('post_label'),
  trendLabel('trend_label'),
  opinionLabel('opinion_label'),
  commentLabel('comment_label'),
  circleLabel('circle_label'),
  notificationLabel('notification_label'),

  // Error Strings
  noInternetConnectionText('no_internet_connection_text'),
  somethingWentWrongText('something_went_wrong_text'),

  // Video Poster Strings
  videoPosterFailedText('video_poster_failed_text'),
  videoPosterFailedDescription('video_poster_failed_description'),
  closeLabel('close_label'),
  videoPosterDownloadActionText('video_poster_download_action_text'),
  videoPosterWhatsappActionText('video_poster_whatsapp_action_text'),
  videoPosterExternalShareActionText('video_poster_external_share_action_text'),
  videoPosterDownloadSuccessText('video_poster_download_success_text'),
  videoPosterWhatsappSuccessText('video_poster_whatsapp_success_text'),
  videoPosterExternalShareSuccessText('video_poster_external_share_success_text'),

  // Shortcut Titles
  shortcutCirclesTitle('shortcut_circles_title'),
  shortcutCreatePostTitle('shortcut_create_post_title'),

  // Profession Selection
  professionSelectionPageTitle('profession_selection_page_title'),
  professionSelectionSkipLabel('profession_selection_skip_label'),
  professionSelectionDoneLabel('profession_selection_done_label'),
  professionBackPressToastMessage('profession_back_press_toast_message'),

  // Support Flow
  supportRequestSentMessage('support_request_sent_message'),
  supportSheetCloseMessage('support_sheet_close_message'),

  //Video Poster Status Page String Keys
  processingLabel('processing_label'),
  downloadingLabel('downloading_label'),
  failedLabel('failed_label'),
  downloadCompletedLabel('download_completed_label'),
  ;

  final String value;
  const StringKey(this.value);
}

extension GetStrings on BuildContext {
  LangCode getLangCode() {
    return Provider.of<StringStore>(this).langCode;
  }

  String getString(StringKey stringKey,
      {Map<String, dynamic> parameters = const {}, bool listen = true}) {
    return Provider.of<StringStore>(this, listen: listen)
        .getString(stringKey, parameters: parameters);
  }

  String getPluralizedString(StringKey stringKey, int count,
      {Map<String, dynamic> parameters = const {}, bool listen = true}) {
    return Provider.of<StringStore>(this, listen: listen)
        .getPluralizedString(stringKey, count, parameters: parameters);
  }
}
