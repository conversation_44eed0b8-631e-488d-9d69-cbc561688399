name: praja
description: Connecting people with common interests, and location
publish_to: none
# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html

version: 2506.09.02+25060902

environment:
  sdk: ">=3.0.0 <4.0.0"
  flutter: ">=3.19.0 <3.21.0" # Started at 1.5.4 :D

platforms:
  android:
  ios:

dependencies:
  flutter:
    sdk: flutter
  screen_recorder:
    path: ./plugins/screenrecorder/
  app_tracking_transparency: ^2.0.3
  achievement_view:
    git:
      url: https://github.com/praja/achievement_view_flutter
      ref: master
  auto_size_text:
    path: ./plugins/auto_size_text/
  photo_manager: ^3.0.0
  bloc: ^8.1.4
  cached_network_image: ^3.3.1
  carousel_slider: ^4.2.1
  chewie: ^1.8.1
  clipboard: ^0.1.2+8
  contacts_service:
    path: ./plugins/contacts_service/
  extended_nested_scroll_view: ^6.2.1
  dio: ^5.4.3+1
  flare_flutter: ^3.0.2
  flutter_animation_progress_bar: ^2.2.1
  encrypt: ^5.0.1
  equatable: ^2.0.5
  extended_text_field: ^14.0.0
  extended_text: ^13.0.0
  firebase_analytics: ^10.10.1
  floor: ^1.5.0
  firebase_core: ^2.29.0
  firebase_crashlytics: ^3.5.1
  firebase_messaging: ^14.8.1
  firebase_performance: ^0.9.4+1
  firebase_remote_config: ^4.4.1
  flutter_animator: ^3.1.2
  flutter_bloc: ^8.1.5
  flutter_local_notifications: ^17.0.1
  flutter_pagewise: ^2.0.1
  flutter_svg: ^2.0.10+1
  expandable_text: ^2.2.0  #copy widget and remove in some other time
  # flutter_share_me is a poorly maintained pub, get rid of this by copying necessary code
  flutter_staggered_grid_view: ^0.4.0
  flutter_vibrate:
    path: ./plugins/flutter_vibrate/

  visibility_detector: ^0.4.0+2
  fluttertoast: ^8.2.5
  get_it: ^7.7.0
  injectable: ^2.1.0
  google_speech: ^4.1.0
  image_gallery_saver:
    path: ./plugins/image_gallery_saver/
  image_picker: ^1.1.0
  in_app_update: ^4.0.1
  in_app_review: ^2.0.9
  intl: ^0.18.1
  json_annotation: ^4.9.0
  linkify: ^4.1.0
  lottie: ^3.1.2
  flick_video_player: ^0.8.0
  infinite_scroll_pagination: ^3.1.0
  path_provider: ^2.0.13
  path: ^1.8.0
  permission_handler: ^11.0.1
  phone_numbers_parser: ^7.0.0+1
  photo_view: ^0.15.0
  provider: ^6.0.0
  quick_actions: ^1.0.7
  recase: ^4.0.0
  receive_sharing_intent:
    path: ./plugins/receive_sharing_intent/
  praja_posters:
    path: ./plugins/praja_posters/
  voice_to_text:
    git:
      url: **************:praja/voice_to_text.git
      ref: v0.0.8
  smart_auth: ^2.0.0
  screenshot: ^2.1.0
  socket_io_client: ^3.0.0-beta.2
  share_plus: ^9.0.0
  shared_preferences: ^2.2.3
  shimmer: ^3.0.0
  android_sms_retriever:
    path: ./plugins/android_sms_retriever/
  singular_flutter_sdk: ^1.4.1
  super_rich_text:
    git:
      url: https://github.com/praja/super_rich_text
      branch: master
  # thumbnails:
  #   git:
  #     url: https://github.com/mackhan007/Flutter_Thumbnails.git
  #     ref: macky
  timeago: ^3.6.1
  truecaller_sdk: # 1.0.0 has big breaking changes and previous versions are not AGP 8 compatible
    path: ./plugins/truecaller_sdk/
  tutorial_coach_mark: ^1.2.11
  url_launcher: ^6.2.6
  ulid: ^2.0.0
  video_player: ^2.4.5
  video_thumbnail:
    path: ./plugins/video_thumbnail/
  webview_flutter: ^4.7.0
  dotted_border: ^2.0.0+3
  file: ^7.0.0
  sqflite: ^2.2.5
  light_compressor:
    path: ./plugins/light_compressor
  hive_flutter: ^1.1.0
  rxdart: ^0.27.7
  scrollable_positioned_list: ^0.3.8
  device_info_plus: ^10.1.0
  gradient_borders: ^1.0.0
  mime: ^1.0.5
  media_store_plus: ^0.1.1
  http: ^1.2.1
  universal_html: ^2.2.2
  google_fonts: ^6.2.1
  async: ^2.10.0
  crypto: ^3.0.3
  palette_generator: ^0.3.3+3
  marquee: ^2.2.3
  heif_converter:
    path: ./plugins/heif_converter/
  connectivity_plus: ^6.0.3
  wakelock_plus: ^1.2.4
  facebook_app_events: ^0.19.2
  flutter_image_compress: ^2.2.0
  flutter_keyboard_visibility: ^6.0.0
  mixpanel_flutter: ^2.3.1
  package_info_plus: ^7.0.0
  flutter_sound: ^9.4.6
  json_serializable: ^6.8.0
  jetpack: ^1.0.7
  flutter_facebook_app_links: # needed ios FBSDKCoreKit ~> 17.0 https://github.com/Mapk26/flutter_facebook_app_links/issues/32
    path: ./plugins/flutter_facebook_app_links/
  font_awesome_flutter: ^10.7.0
  yaml: ^3.1.2
  infinite_carousel: ^1.1.1
  flutter_cache_manager: ^3.4.1
  hypersdkflutter: ^4.0.20
  uuid: ^4.4.2
  store_checker: ^1.6.0
  collection: ^1.18.0
  flutter_downloader: ^1.11.8
  image_cropper: ^8.0.1
  logger: ^2.5.0

dependency_overrides:
  video_player_android: #override details: https://www.notion.so/praja-app/Add-cache-and-customize-buffer-values-for-Android-video-player-7d586c497409484b86919cd334fe3c7e?pvs=4
    git:
      url: https://github.com/praja/flutter_packages
      path: packages/video_player/video_player_android
      ref: video_player_android_praja-v1.4

dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter
  build_runner: ^2.3.3
  injectable_generator: ^2.1.4
  flutter_launcher_icons: ^0.14.3
  floor_generator: ^1.5.0
  flutter_lints: ^3.0.2
  hive_generator: ^2.0.0
  custom_lint: ^0.6.4
  hardcoded_strings_lint_check:
    path: ./plugins/hardcoded_strings_lint_check/

  #json_serializable: ^3.5.1

# EXECUTE - flutter pub run flutter_launcher_icons:main
flutter_icons:
  android: true
  ios: true
  image_path_android: "assets/images/app-icon.png"
  image_path_ios: "assets/images/app-icon.png"

# For information on the generic Dart part of this file, see the
# following page: https://www.dartlang.org/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/strings/en.yaml
    - assets/strings/te.yaml
    - assets/mic_blue.flr
    - assets/pubKey.pem
    - assets/images/app-icon.jpg
    - assets/images/app-icon-premium.png
    - assets/images/logo.jpg
    - assets/images/logo-transparent.png
    - assets/images/praja-logo-watermark.png
    - assets/images/praja-header-logo.png
    - assets/images/praja-header-name.png
    - assets/images/praja-full-logo.png
    - assets/images/intro/slide-1.jpg
    - assets/images/intro/slide-2.jpg
    - assets/images/intro/slide-3.jpg
    - assets/images/intro/slide-4.jpg
    - assets/images/icons/camera.png
    - assets/images/icons/post-share-app-icon.png
    - assets/images/icons/Green-Blank-profile.png
    - assets/images/icons/poster-app-logo.png
    - assets/images/truecaller.png
    - assets/images/banner_crowd.jpg
    - assets/images/chequered_bg.png
    - assets/images/invite_bg.png
    - assets/images/action/
    - assets/images/badges/
    - assets/images/create/
    - assets/images/glassy.png
    - assets/symmetric-axle-332608-809dd6b09bf2.json
    - assets/images/badge_congo/
    - assets/Lottie/
    - assets/google_fonts/
    - assets/images/circle-official-star.png
    - assets/images/lead_section_icons/
    - assets/images/premium-offer-gift.png

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.io/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.io/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: CircleIcons
      fonts:
        - asset: assets/fonts/CircleIcons.ttf
    - family: OrgPageIcons
      fonts:
        - asset: assets/fonts/OrgPageIcons.ttf
    - family: AdminOptionsIcon
      fonts:
        - asset: assets/fonts/AdminOptionsIcon.ttf
    - family: PosterIcons
      fonts:
        - asset: assets/fonts/PosterIcons.ttf
    - family: InfoIcons
      fonts:
        - asset: assets/fonts/InfoIcons.ttf
    - family: FollowContactIcons
      fonts:
        - asset: assets/fonts/FollowContactIcons.ttf
    - family: PrajaIcons
      fonts:
        - asset: assets/fonts/PrajaIcons.ttf
    - family: Roboto
      fonts:
        - asset: assets/google_fonts/Roboto-Regular.ttf
        - asset: assets/google_fonts/Roboto-Medium.ttf
          weight: 500
        - asset: assets/google_fonts/Roboto-Bold.ttf
          weight: 700
        - asset: assets/google_fonts/Roboto-Black.ttf
          weight: 900
        - asset: assets/google_fonts/Roboto-Light.ttf
          weight: 300
        - asset: assets/google_fonts/Roboto-Thin.ttf
          weight: 100
    - family: NotoSansTelugu
      fonts:
        - asset: assets/google_fonts/NotoSansTelugu-Regular.ttf
        - asset: assets/google_fonts/NotoSansTelugu-Medium.ttf
          weight: 500
        - asset: assets/google_fonts/NotoSansTelugu-Bold.ttf
          weight: 700
        - asset: assets/google_fonts/NotoSansTelugu-Black.ttf
          weight: 900
        - asset: assets/google_fonts/NotoSansTelugu-Light.ttf
          weight: 300
        - asset: assets/google_fonts/NotoSansTelugu-Thin.ttf
          weight: 100

#
# For details regarding fonts from package dependencies,
# see https://flutter.io/custom-fonts/#from-packages
